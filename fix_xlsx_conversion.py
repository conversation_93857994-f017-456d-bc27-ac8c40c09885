#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to properly fix the XLSX file conversions
"""

import pandas as pd
import os

def fix_cs_300_mile_radius():
    """Fix the cs_300_mile_radius_pgh.xlsx conversion"""
    print("Fixing cs_300_mile_radius_pgh.xlsx...")
    
    try:
        # Read the XLSX file with proper header row (row 2, which is index 2)
        df = pd.read_excel("cs_300_mile_radius_pgh.xlsx", header=2)
        
        # Remove completely empty columns
        df = df.dropna(axis=1, how='all')
        
        # Remove completely empty rows
        df = df.dropna(axis=0, how='all')
        
        # Clean column names
        df.columns = df.columns.astype(str).str.strip().str.replace(' ', '_').str.replace('[^a-zA-Z0-9_]', '', regex=True).str.lower()
        
        # Rename columns to match standard format
        column_mapping = {
            'name': 'company',
            'email': 'email',
            'website_url': 'website_url',
            'facebook_url': 'facebook_url',
            'instagram_url': 'instagram_url',
            'twitter_url': 'twitter_url',
            'linkedin_url': 'linkedin_url',
            'phone': 'phone',
            'address': 'address',
            'city': 'city',
            'state': 'state',
            'zip_code': 'zip_code',
            'type': 'business_type',
            'industry': 'industry'
        }
        
        # Apply column mapping
        df = df.rename(columns=column_mapping)
        
        # Add missing standard columns
        standard_columns = ['first_name', 'last_name', 'title', 'headline', 'seniority']
        for col in standard_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Save to CSV
        df.to_csv("cs_300_mile_radius_pgh.csv", index=False)
        print(f"✓ Successfully fixed cs_300_mile_radius_pgh.csv")
        print(f"  Records: {len(df)}")
        print(f"  Columns: {len(df.columns)}")
        print(f"  Sample columns: {list(df.columns[:10])}")
        
        return df
        
    except Exception as e:
        print(f"✗ Error fixing cs_300_mile_radius_pgh.xlsx: {e}")
        return None

def fix_super_local_pgh_cs():
    """Fix the super_local_pgh_cs.xlsx conversion"""
    print("Fixing super_local_pgh_cs.xlsx...")
    
    try:
        # Read the XLSX file with proper header row (row 2, which is index 2)
        df = pd.read_excel("super_local_pgh_cs.xlsx", header=2)
        
        # Remove completely empty columns
        df = df.dropna(axis=1, how='all')
        
        # Remove completely empty rows
        df = df.dropna(axis=0, how='all')
        
        # Clean column names
        df.columns = df.columns.astype(str).str.strip().str.replace(' ', '_').str.replace('[^a-zA-Z0-9_]', '', regex=True).str.lower()
        
        # Rename columns to match standard format
        column_mapping = {
            'name': 'company',
            'email': 'email',
            'url': 'website_url',
            'social_url': 'social_media_url',
            'phone_number': 'phone',
            'additional_emails': 'additional_emails'
        }
        
        # Apply column mapping
        df = df.rename(columns=column_mapping)
        
        # Add missing standard columns
        standard_columns = ['first_name', 'last_name', 'title', 'headline', 'seniority', 'city', 'state', 'industry']
        for col in standard_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Save to CSV
        df.to_csv("super_local_pgh_cs.csv", index=False)
        print(f"✓ Successfully fixed super_local_pgh_cs.csv")
        print(f"  Records: {len(df)}")
        print(f"  Columns: {len(df.columns)}")
        print(f"  Sample columns: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"✗ Error fixing super_local_pgh_cs.xlsx: {e}")
        return None

def main():
    """Main function to fix XLSX conversions"""
    print("Fixing XLSX file conversions...\n")
    
    # Fix both XLSX files
    fix_cs_300_mile_radius()
    print()
    fix_super_local_pgh_cs()
    
    print("\nFixed conversions complete!")
    
    # Show summary of all CSV files
    print("\nSummary of all CSV files:")
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    for csv_file in sorted(csv_files):
        try:
            df = pd.read_csv(csv_file)
            print(f"  {csv_file}: {len(df)} records, {len(df.columns)} columns")
        except Exception as e:
            print(f"  {csv_file}: Error reading file - {e}")

if __name__ == "__main__":
    main()
