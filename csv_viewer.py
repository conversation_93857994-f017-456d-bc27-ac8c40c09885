#!/usr/bin/env python3
"""
Simple CSV Viewer with UI for exploring lead data
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from pathlib import Path

class CSVViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Lead Data CSV Viewer")
        self.root.geometry("1200x800")
        
        self.current_df = None
        self.current_file = None
        
        self.setup_ui()
        self.load_csv_files()
        self.update_total_stats()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Select CSV File", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        ttk.Label(file_frame, text="File:").grid(row=0, column=0, sticky="w", padx=(0, 5))

        self.file_var = tk.StringVar()
        self.file_combo = ttk.Combobox(file_frame, textvariable=self.file_var, state="readonly")
        self.file_combo.grid(row=0, column=1, sticky="ew", padx=(0, 5))
        self.file_combo.bind('<<ComboboxSelected>>', self.on_file_selected)

        ttk.Button(file_frame, text="Refresh", command=self.load_csv_files).grid(row=0, column=2)

        # Stats frame
        self.stats_frame = ttk.LabelFrame(main_frame, text="Data Summary", padding="5")
        self.stats_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Data frame
        data_frame = ttk.LabelFrame(main_frame, text="Data View", padding="5")
        data_frame.grid(row=2, column=0, columnspan=2, sticky="nsew")
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(0, weight=1)

        # Treeview for data display
        self.tree = ttk.Treeview(data_frame)
        self.tree.grid(row=0, column=0, sticky="nsew")

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(data_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # Total database stats frame
        self.total_stats_frame = ttk.LabelFrame(main_frame, text="Total Lead Database Statistics", padding="5")
        self.total_stats_frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.total_stats_frame.columnconfigure(0, weight=1)
        self.total_stats_frame.columnconfigure(1, weight=1)
        self.total_stats_frame.columnconfigure(2, weight=1)
    
    def load_csv_files(self):
        """Load available CSV files"""
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        self.file_combo['values'] = csv_files
        
        if csv_files and not self.file_var.get():
            self.file_var.set(csv_files[0])
            self.load_csv_data(csv_files[0])
    
    def on_file_selected(self, event=None):
        """Handle file selection"""
        selected_file = self.file_var.get()
        if selected_file:
            self.load_csv_data(selected_file)
    
    def load_csv_data(self, filename):
        """Load and display CSV data"""
        try:
            self.current_df = pd.read_csv(filename)
            self.current_file = filename
            self.display_data()
            self.update_stats()
            self.update_total_stats()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}:\n{str(e)}")
    
    def display_data(self, df=None):
        """Display data in the treeview"""
        if df is None:
            df = self.current_df
        
        if df is None:
            return
        
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Setup columns
        columns = list(df.columns)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col)
            # Set column width based on content
            max_width = max(
                len(str(col)) * 8,  # Header width
                df[col].astype(str).str.len().max() * 8 if not df[col].empty else 50
            )
            self.tree.column(col, width=min(max_width, 200), minwidth=50)
        
        # Insert data (limit to first 1000 rows for performance)
        display_df = df.head(1000)
        for index, row in display_df.iterrows():
            values = [str(val) if pd.notna(val) else '' for val in row]
            self.tree.insert('', 'end', values=values)
        
        if len(df) > 1000:
            # Add a note about truncation
            note_values = ['...'] + ['(Showing first 1000 rows)'] + [''] * (len(columns) - 2)
            self.tree.insert('', 'end', values=note_values)
    
    def update_stats(self):
        """Update statistics display"""
        if self.current_df is None:
            return
        
        # Clear existing stats
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        df = self.current_df
        
        # Basic stats
        stats_text = f"File: {self.current_file}\n"
        stats_text += f"Total Records: {len(df):,}\n"
        stats_text += f"Total Columns: {len(df.columns)}\n"
        
        # Email stats
        if 'email' in df.columns:
            email_count = df['email'].notna().sum()
            stats_text += f"Records with Email: {email_count:,} ({email_count/len(df)*100:.1f}%)\n"
        
        # Company stats
        if 'company' in df.columns:
            unique_companies = df['company'].nunique()
            stats_text += f"Unique Companies: {unique_companies:,}\n"
        
        # Location stats
        if 'state' in df.columns:
            unique_states = df['state'].nunique()
            stats_text += f"States Represented: {unique_states}\n"
        
        # Industry stats
        if 'industry' in df.columns:
            unique_industries = df['industry'].nunique()
            stats_text += f"Industries: {unique_industries}\n"
        
        # Social media stats
        social_cols = ['linkedin_url', 'facebook_url', 'twitter_url', 'instagram_url']
        social_stats = []
        for col in social_cols:
            if col in df.columns:
                count = df[col].notna().sum()
                platform = col.replace('_url', '').replace('_', ' ').title()
                social_stats.append(f"{platform}: {count}")
        
        if social_stats:
            stats_text += f"Social Media Links: {', '.join(social_stats)}\n"
        
        # Display stats
        stats_label = ttk.Label(self.stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.grid(row=0, column=0, sticky="nw")

        # Top companies/industries
        if 'company' in df.columns:
            top_companies = df['company'].value_counts().head(5)
            if not top_companies.empty:
                companies_text = "Top Companies:\n" + "\n".join([f"• {comp}: {count}" for comp, count in top_companies.items()])
                companies_label = ttk.Label(self.stats_frame, text=companies_text, justify=tk.LEFT)
                companies_label.grid(row=0, column=1, sticky="nw", padx=(20, 0))

        if 'industry' in df.columns:
            top_industries = df['industry'].value_counts().head(5)
            if not top_industries.empty:
                industries_text = "Top Industries:\n" + "\n".join([f"• {ind}: {count}" for ind, count in top_industries.items()])
                industries_label = ttk.Label(self.stats_frame, text=industries_text, justify=tk.LEFT)
                industries_label.grid(row=0, column=2, sticky="nw", padx=(20, 0))
    
    def update_total_stats(self):
        """Update total database statistics"""
        # Clear existing stats
        for widget in self.total_stats_frame.winfo_children():
            widget.destroy()

        # Load all CSV files and combine stats
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]

        total_records = 0
        total_with_email = 0
        total_with_linkedin = 0
        total_with_facebook = 0
        total_with_twitter = 0
        total_with_instagram = 0
        total_with_website = 0
        total_with_phone = 0
        all_companies = set()
        all_industries = set()
        all_states = set()

        file_stats = []

        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                records = len(df)
                total_records += records

                # Email coverage
                email_count = df['email'].notna().sum() if 'email' in df.columns else 0
                total_with_email += email_count

                # Social media coverage
                linkedin_count = df['linkedin_url'].notna().sum() if 'linkedin_url' in df.columns else 0
                facebook_count = df['facebook_url'].notna().sum() if 'facebook_url' in df.columns else 0
                twitter_count = df['twitter_url'].notna().sum() if 'twitter_url' in df.columns else 0
                instagram_count = df['instagram_url'].notna().sum() if 'instagram_url' in df.columns else 0

                total_with_linkedin += linkedin_count
                total_with_facebook += facebook_count
                total_with_twitter += twitter_count
                total_with_instagram += instagram_count

                # Website and phone coverage
                website_count = df['website_url'].notna().sum() if 'website_url' in df.columns else 0
                phone_count = df['phone'].notna().sum() if 'phone' in df.columns else 0
                total_with_website += website_count
                total_with_phone += phone_count

                # Unique companies, industries, states
                if 'company' in df.columns:
                    all_companies.update(df['company'].dropna().unique())
                if 'industry' in df.columns:
                    all_industries.update(df['industry'].dropna().unique())
                if 'state' in df.columns:
                    all_states.update(df['state'].dropna().unique())

                # File-specific stats
                file_stats.append({
                    'file': csv_file,
                    'records': records,
                    'email_coverage': f"{email_count/records*100:.1f}%" if records > 0 else "0%"
                })

            except Exception as e:
                print(f"Error reading {csv_file}: {e}")

        # Create summary text
        summary_text = f"TOTAL LEAD DATABASE\n"
        summary_text += f"Total Records: {total_records:,}\n"
        summary_text += f"Unique Companies: {len(all_companies):,}\n"
        summary_text += f"Industries: {len(all_industries)}\n"
        summary_text += f"States: {len(all_states)}\n\n"
        summary_text += f"DATA COVERAGE\n"
        summary_text += f"Email: {total_with_email:,} ({total_with_email/total_records*100:.1f}%)\n"
        summary_text += f"Phone: {total_with_phone:,} ({total_with_phone/total_records*100:.1f}%)\n"
        summary_text += f"Website: {total_with_website:,} ({total_with_website/total_records*100:.1f}%)"

        social_text = f"SOCIAL MEDIA COVERAGE\n"
        social_text += f"LinkedIn: {total_with_linkedin:,} ({total_with_linkedin/total_records*100:.1f}%)\n"
        social_text += f"Facebook: {total_with_facebook:,} ({total_with_facebook/total_records*100:.1f}%)\n"
        social_text += f"Twitter: {total_with_twitter:,} ({total_with_twitter/total_records*100:.1f}%)\n"
        social_text += f"Instagram: {total_with_instagram:,} ({total_with_instagram/total_records*100:.1f}%)\n\n"
        social_text += f"ENRICHMENT POTENTIAL\n"
        social_text += f"Ready for Web Scraping: {total_with_website:,}\n"
        social_text += f"Ready for Social Research: {total_with_linkedin + total_with_facebook:,}\n"
        social_text += f"Multi-channel Outreach: {min(total_with_email, total_with_linkedin):,}"

        files_text = "FILE BREAKDOWN\n"
        for stat in file_stats:
            files_text += f"• {stat['file']}: {stat['records']} records ({stat['email_coverage']} email)\n"

        # Display stats in three columns
        summary_label = ttk.Label(self.total_stats_frame, text=summary_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        summary_label.grid(row=0, column=0, sticky="nw", padx=(0, 20))

        social_label = ttk.Label(self.total_stats_frame, text=social_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        social_label.grid(row=0, column=1, sticky="nw", padx=(0, 20))

        files_label = ttk.Label(self.total_stats_frame, text=files_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        files_label.grid(row=0, column=2, sticky="nw")

def main():
    """Main function to run the CSV viewer"""
    root = tk.Tk()
    app = CSVViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
