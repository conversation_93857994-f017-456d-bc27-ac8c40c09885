#!/usr/bin/env python3
"""
Simple CSV Viewer with UI for exploring lead data
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from pathlib import Path

class CSVViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Lead Data CSV Viewer")
        self.root.geometry("1200x800")
        
        self.current_df = None
        self.current_file = None
        
        self.setup_ui()
        self.load_csv_files()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Select CSV File", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.file_var = tk.StringVar()
        self.file_combo = ttk.Combobox(file_frame, textvariable=self.file_var, state="readonly")
        self.file_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.file_combo.bind('<<ComboboxSelected>>', self.on_file_selected)
        
        ttk.Button(file_frame, text="Refresh", command=self.load_csv_files).grid(row=0, column=2)
        
        # Stats frame
        self.stats_frame = ttk.LabelFrame(main_frame, text="Data Summary", padding="5")
        self.stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Data frame
        data_frame = ttk.LabelFrame(main_frame, text="Data View", padding="5")
        data_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(0, weight=1)
        
        # Treeview for data display
        self.tree = ttk.Treeview(data_frame)
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(data_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Search frame
        search_frame = ttk.LabelFrame(main_frame, text="Search & Filter", padding="5")
        search_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="Search:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        ttk.Button(search_frame, text="Clear", command=self.clear_search).grid(row=0, column=2)
    
    def load_csv_files(self):
        """Load available CSV files"""
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        self.file_combo['values'] = csv_files
        
        if csv_files and not self.file_var.get():
            self.file_var.set(csv_files[0])
            self.load_csv_data(csv_files[0])
    
    def on_file_selected(self, event=None):
        """Handle file selection"""
        selected_file = self.file_var.get()
        if selected_file:
            self.load_csv_data(selected_file)
    
    def load_csv_data(self, filename):
        """Load and display CSV data"""
        try:
            self.current_df = pd.read_csv(filename)
            self.current_file = filename
            self.display_data()
            self.update_stats()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}:\n{str(e)}")
    
    def display_data(self, df=None):
        """Display data in the treeview"""
        if df is None:
            df = self.current_df
        
        if df is None:
            return
        
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Setup columns
        columns = list(df.columns)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col)
            # Set column width based on content
            max_width = max(
                len(str(col)) * 8,  # Header width
                df[col].astype(str).str.len().max() * 8 if not df[col].empty else 50
            )
            self.tree.column(col, width=min(max_width, 200), minwidth=50)
        
        # Insert data (limit to first 1000 rows for performance)
        display_df = df.head(1000)
        for index, row in display_df.iterrows():
            values = [str(val) if pd.notna(val) else '' for val in row]
            self.tree.insert('', 'end', values=values)
        
        if len(df) > 1000:
            # Add a note about truncation
            note_values = ['...'] + ['(Showing first 1000 rows)'] + [''] * (len(columns) - 2)
            self.tree.insert('', 'end', values=note_values)
    
    def update_stats(self):
        """Update statistics display"""
        if self.current_df is None:
            return
        
        # Clear existing stats
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        df = self.current_df
        
        # Basic stats
        stats_text = f"File: {self.current_file}\n"
        stats_text += f"Total Records: {len(df):,}\n"
        stats_text += f"Total Columns: {len(df.columns)}\n"
        
        # Email stats
        if 'email' in df.columns:
            email_count = df['email'].notna().sum()
            stats_text += f"Records with Email: {email_count:,} ({email_count/len(df)*100:.1f}%)\n"
        
        # Company stats
        if 'company' in df.columns:
            unique_companies = df['company'].nunique()
            stats_text += f"Unique Companies: {unique_companies:,}\n"
        
        # Location stats
        if 'state' in df.columns:
            unique_states = df['state'].nunique()
            stats_text += f"States Represented: {unique_states}\n"
        
        # Industry stats
        if 'industry' in df.columns:
            unique_industries = df['industry'].nunique()
            stats_text += f"Industries: {unique_industries}\n"
        
        # Social media stats
        social_cols = ['linkedin_url', 'facebook_url', 'twitter_url', 'instagram_url']
        social_stats = []
        for col in social_cols:
            if col in df.columns:
                count = df[col].notna().sum()
                platform = col.replace('_url', '').replace('_', ' ').title()
                social_stats.append(f"{platform}: {count}")
        
        if social_stats:
            stats_text += f"Social Media Links: {', '.join(social_stats)}\n"
        
        # Display stats
        stats_label = ttk.Label(self.stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.grid(row=0, column=0, sticky=(tk.W, tk.N))
        
        # Top companies/industries
        if 'company' in df.columns:
            top_companies = df['company'].value_counts().head(5)
            if not top_companies.empty:
                companies_text = "Top Companies:\n" + "\n".join([f"• {comp}: {count}" for comp, count in top_companies.items()])
                companies_label = ttk.Label(self.stats_frame, text=companies_text, justify=tk.LEFT)
                companies_label.grid(row=0, column=1, sticky=(tk.W, tk.N), padx=(20, 0))
        
        if 'industry' in df.columns:
            top_industries = df['industry'].value_counts().head(5)
            if not top_industries.empty:
                industries_text = "Top Industries:\n" + "\n".join([f"• {ind}: {count}" for ind, count in top_industries.items()])
                industries_label = ttk.Label(self.stats_frame, text=industries_text, justify=tk.LEFT)
                industries_label.grid(row=0, column=2, sticky=(tk.W, tk.N), padx=(20, 0))
    
    def on_search(self, event=None):
        """Handle search functionality"""
        if self.current_df is None:
            return
        
        search_term = self.search_var.get().lower()
        
        if not search_term:
            self.display_data()
            return
        
        # Search across all string columns
        mask = pd.Series([False] * len(self.current_df))
        
        for col in self.current_df.columns:
            if self.current_df[col].dtype == 'object':  # String columns
                mask |= self.current_df[col].astype(str).str.lower().str.contains(search_term, na=False)
        
        filtered_df = self.current_df[mask]
        self.display_data(filtered_df)
        
        # Update search result count
        result_count = len(filtered_df)
        self.root.title(f"Lead Data CSV Viewer - Search Results: {result_count} records")
    
    def clear_search(self):
        """Clear search and show all data"""
        self.search_var.set('')
        self.display_data()
        self.root.title("Lead Data CSV Viewer")

def main():
    """Main function to run the CSV viewer"""
    root = tk.Tk()
    app = CSVViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
