#!/usr/bin/env python3
"""
Script to convert XLSX files and JSON text file to CSV format
"""

import pandas as pd
import json
import os
from pathlib import Path

def convert_json_to_csv(json_file_path, output_csv_path):
    """Convert JSON text file to CSV"""
    print(f"Converting {json_file_path} to CSV...")
    
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Reorder columns to match the existing CSV format (based on pa_category_managers.csv)
        desired_columns = [
            'first_name', 'last_name', 'name', 'email', 'title', 'headline',
            'organization_name', 'organization_website_url', 'linkedin_url',
            'facebook_url', 'twitter_url', 'photo_url',
            'city', 'state', 'country',
            'organization_city', 'organization_state', 'organization_country',
            'organization_street_address', 'organization_postal_code',
            'organization_phone', 'organization_primary_domain',
            'industry', 'seniority', 'estimated_num_employees',
            'organization_annual_revenue', 'organization_annual_revenue_printed',
            'organization_founded_year', 'organization_short_description',
            'organization_linkedin_url', 'organization_facebook_url',
            'organization_twitter_url', 'organization_logo_url',
            'keywords', 'organization_technologies',
            'id', 'organization_id', 'organization_linkedin_uid',
            'email_domain_catchall', 'personal_email',
            'organization_market_cap', 'organization_total_funding',
            'organization_total_funding_printed', 'organization_seo_description',
            'organization_raw_address'
        ]
        
        # Reorder columns, adding missing ones as empty
        for col in desired_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Select and reorder columns
        df = df[desired_columns]
        
        # Save to CSV
        df.to_csv(output_csv_path, index=False)
        print(f"✓ Successfully converted to {output_csv_path}")
        print(f"  Records: {len(df)}")
        
        return df
        
    except Exception as e:
        print(f"✗ Error converting {json_file_path}: {e}")
        return None

def convert_xlsx_to_csv(xlsx_file_path, output_csv_path):
    """Convert XLSX file to CSV"""
    print(f"Converting {xlsx_file_path} to CSV...")

    try:
        # Read the XLSX file - try different header rows
        df = None

        # First, try to read and find the actual header row
        temp_df = pd.read_excel(xlsx_file_path, header=None, nrows=10)

        # Look for the row that contains the most non-null values (likely the header)
        header_row = 0
        max_non_null = 0

        for i in range(min(5, len(temp_df))):
            non_null_count = temp_df.iloc[i].notna().sum()
            if non_null_count > max_non_null:
                max_non_null = non_null_count
                header_row = i

        print(f"  Found header row at index: {header_row}")

        # Read with the correct header row
        df = pd.read_excel(xlsx_file_path, header=header_row)

        # Remove completely empty columns
        df = df.dropna(axis=1, how='all')

        # Clean column names (remove spaces, special characters)
        df.columns = df.columns.astype(str).str.strip().str.replace(' ', '_').str.replace('[^a-zA-Z0-9_]', '', regex=True).str.lower()

        # Remove rows that are completely empty
        df = df.dropna(axis=0, how='all')

        # Save to CSV
        df.to_csv(output_csv_path, index=False)
        print(f"✓ Successfully converted to {output_csv_path}")
        print(f"  Records: {len(df)}")
        print(f"  Columns: {len(df.columns)}")
        print(f"  Sample columns: {list(df.columns[:10])}")

        return df

    except Exception as e:
        print(f"✗ Error converting {xlsx_file_path}: {e}")
        return None

def main():
    """Main conversion function"""
    print("Starting file conversion to CSV format...\n")
    
    # Convert JSON files
    json_files = [
        "super_local_ceo_contacts.txt",
        "super_local_restaurants.txt"
    ]

    for json_file in json_files:
        if os.path.exists(json_file):
            # Create output filename
            output_file = json_file.replace('.txt', '.csv')
            convert_json_to_csv(json_file, output_file)
        else:
            print(f"✗ JSON file {json_file} not found")
        print()
    
    print()
    
    # Convert XLSX files
    xlsx_files = [
        "cs_300_mile_radius_pgh.xlsx",
        "super_local_pgh_cs.xlsx"
    ]
    
    for xlsx_file in xlsx_files:
        if os.path.exists(xlsx_file):
            # Create output filename
            output_file = xlsx_file.replace('.xlsx', '.csv')
            convert_xlsx_to_csv(xlsx_file, output_file)
        else:
            print(f"✗ XLSX file {xlsx_file} not found")
        print()
    
    print("Conversion complete!")
    
    # Show summary of all CSV files
    print("\nSummary of all CSV files:")
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    for csv_file in sorted(csv_files):
        try:
            df = pd.read_csv(csv_file)
            print(f"  {csv_file}: {len(df)} records, {len(df.columns)} columns")
        except Exception as e:
            print(f"  {csv_file}: Error reading file - {e}")

if __name__ == "__main__":
    main()
